<?php $res = get_permission(session('Adminnewlogin')['type']); 
 if (isset(session('Adminnewlogin')['id'])) {

$record = DB::table('users')->where('id', '=', session('Adminnewlogin')['id'])->first();
$img = $record->image;
if ($img) {
    $imgurl = $record->image;
}
$name = $record->first_name . ' ' . $record->last_name;
}
?>

<style>

@media (max-width: 767px){

    .mobile-mega-menu{    left: auto !important;
        right: 0 !important;
        top: 52px !important;}
    }
    .scrollable_div::-webkit-scrollbar {
      width: 3px;
    }

    .scrollable_div::-webkit-scrollbar-button {
      height: 5px;
    }

    .scrollable_div::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(234, 238, 242, 1);
      border-radius: 15px;
    }

    .scrollable_div::-webkit-scrollbar-thumb {
      background-color: rgba(0, 90, 168, 1);
      border-radius: 15px;
    }
</style>
  <nav class="navbar navbar-expand navbar-light navbar-bg">
      <a class="sidebar-toggle">
          <i class="hamburger align-self-center"></i>
      </a>

      <div class="navbar-collapse collapse">
          <ul class="navbar-nav navbar-align">
            <?php if(isset($res['managenotification'])): ?>
            <?php if(array_key_exists('managenotification', $res)): ?>
                <?php if(in_array('view', json_decode($res['managenotification'], true))): ?>
            <li class="nav-item dropdown">
                <a class="nav-icon dropdown-toggle" href="#" id="alertsDropdown" data-toggle="dropdown">
                    <div class="position-relative">
                        <i class="align-middle" data-feather="bell"></i>
                        <?php if(notificationunread()->count()): ?>
                        <span class="indicator" id="unreadNotifications"><?php echo e(notificationunread()->count()); ?></span>
                        <?php endif; ?>
                    </div>
                  
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-start py-0 mobile-mega-menu" aria-labelledby="alertsDropdown" style="
                left: auto;
                max-height: calc(70vh + 50px);
                right: -200px;
                top: 42px;">
                   
                    <div class="scrollable_div" style="max-height: 70vh; overflow-y: auto;">
                        <?php if(!empty(notificationunread()) && notificationunread()->count()): ?>
                        <?php $__currentLoopData = notificationunread(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="list-group">
                            <div  class="list-group-item">
                                <div class="row g-0 align-items-center">
                                   
                                    <div class="col-10">
                                        
                                        <div class="text-muted small mt-1"><?php echo html_entity_decode($val->notification); ?></div>
                                        <div class="text-muted small mt-1 local-time" data-date="<?php echo e($val->created_at->format('Y-m-d H:i:s')); ?>"> </div>
                                    </div> 
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>

                    <div class="dropdown-menu-footer">
                        <a href="<?php echo e(url('all-notifications')); ?>" class="text-muted">Show all notifications</a>
                    </div>
                </div>
            </li>
            <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
            <li class="nav-item dropdown">
          <a class="nav-icon dropdown-toggle d-inline-block d-sm-none" href="#" data-toggle="dropdown">
              <img src="<?php if(@$imgurl): ?><?php echo e(generateSignedUrl($img)); ?> <?php else: ?> <?php echo e(default_user_placeholder()); ?> <?php endif; ?>" class="avatar img-fluid rounded-circle mr-1" alt="Chris Wood" />
          </a>

          <a class="nav-link dropdown-toggle d-none d-sm-inline-block" href="#" data-toggle="dropdown">
              <img src="<?php if(@$imgurl): ?><?php echo e(generateSignedUrl($img)); ?> <?php else: ?> <?php echo e(default_user_placeholder()); ?> <?php endif; ?>" class="avatar img-fluid rounded-circle mr-1" alt="<?php echo e($name); ?>" />

              <span class="text-dark d-inline-block" style="transform: translateY(-7px);min-width: 150px;">
                <?php echo e(@$name); ?> <br>
                <small id="timezone-element" class="position-absolute"></small>
              </span>
          </a>
          <div class="dropdown-menu dropdown-menu-right">
              <a class="dropdown-item" href="<?php echo e(url('admin-profile')); ?>">
                  <i class="align-middle mr-1" data-feather="user"></i> Profile Details
              </a>
              <a class="dropdown-item" href="<?php echo e(url('edit-admin-profile')); ?>">
                  <i class="align-middle mr-1" data-feather="edit"></i> Edit Profile
              </a>
              <a class="dropdown-item" href="<?php echo e(url('change-admin-password')); ?>">
                  <i class="align-middle mr-1" data-feather="lock"></i> Change Password
              </a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="<?php echo e(url('admin-signout')); ?>">logout</a>
          </div>
      </li>
      </ul>
      </div>
  </nav><?php /**PATH D:\whizara\whizara\resources\views/admin/layouts/top.blade.php ENDPATH**/ ?>