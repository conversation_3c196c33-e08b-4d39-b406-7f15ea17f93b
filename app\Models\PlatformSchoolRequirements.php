<?php

namespace App\Models;

use App\User;
use App\Classes;
use App\Subject;
use App\Models\k12ConnectionCategorizedData;
use App\Models\PlatformSchoolInvites;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\UserOpportunityPreference;
use App\Schools;
use Auth;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class PlatformSchoolRequirements extends Model
{
    use SoftDeletes;
    protected $table = 'platform_school_requirements';
    protected $fillable = [
        'status',
        'is_visible_to_school',
        'requirement_type',
        'requirement_title',
        'school_id',
        'district_school_id',
        'proctor_id',
        'class_type',
        'delivery_mode',
        'subject_area_id',
        'subject_id',
        'grade_levels_id',
        'capacity',
        'description',
        'requirement_tags',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'lat',
        'lng',
        'start_date',
        'end_date',
        'time_zone',
        'other_requirements',
        'benefits',
        'is_valid',
        'totalHours',
        'finalize_setup',
        'profileType_requirements',
        'language_requirements',
        'will_choose_educator',
        'credential_check',
        'special_education_certificate',
        'will_follow_provided_curriculum',
        'provide_schedule_access',
        'no_instrtructional_days',
        'class_duration',
        'no_non_instructional_hr',
        'schedule_type',
        'no_class_dates',
        'regular_days',
        'schedule_1_days',
        'schedule_2_days',
        'sch_cal_screenshot',
        'district_cal_screenshot',
        'teacher_schedule_screenshot',
        'class_details',
        'experience',
        'qualifications',
        'total_budget',
        'benefits',
        'cbo_id',
        'added_by',
        'parent_id',
    ];
    protected $casts = [
        'regular_days'    => 'array',
        'schedule_1_days' => 'array',
        'schedule_2_days' => 'array',
    ];

    protected $appends = ['grade_level_names'];


    protected static function booted()
    {
        static::saved(function ($requirement) {
            $workload = $requirement->calculateWorkingLoad();

            // Only update if value has changed
            if ($requirement->totalHours != $workload['working_hours']) {
                $requirement->totalHours = $workload['working_hours'];
                $requirement->saveQuietly(); // prevent infinite loop of events
            }
        });
    }

    public function subject()
    {
        return $this->belongsTo(V1Subject::class, 'subject_id');
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    public function school()
    {
        return $this->belongsTo(Schools::class, 'school_id');
    }

    public function reviewApplicants()
    {
        return $this->hasMany(SchoolReviewApplicants::class, 'requirement_id');
    }
    public function invites()
    {
        return $this->hasMany(PlatformSchoolInvites::class, 'requirement_id');
    }

    public function classes()
    {
        return $this->hasManyThrough(
            k12ConnectionClasses::class,  // Final table
            k12ConnectionPrograms::class, // Intermediate table
            'requirement_id', // Foreign key in k12ConnectionPrograms (jo `platform_school_requirements` ko reference karega)
            'program_id', // Foreign key in k12ConnectionClasses (jo `k12ConnectionPrograms` ko reference karega)
            'id', // Local key in PlatformSchoolRequirements
            'id' // Local key in k12ConnectionPrograms
        );
    }

    public function getGradeLevelNamesAttribute()
    {
        $ids = explode(',', $this->grade_levels_id);
        return Classes::whereIn('id', $ids)->pluck('class_name')->toArray();
    }

    public function chats()
    {
        return $this->hasMany(Chat::class, 'referance_id', 'id');
    }

    public function proctor()
    {
        return $this->belongsTo(PlatformSchoolProctor::class, 'proctor_id');
    }

    public function meetingLinks()
    {
        return $this->hasMany(k12ConnectionMeetingLinks::class, 'requirement_id');
    }

    public function availibilities()
    {
        return $this->hasMany(k12ConnectionMeetingLinks::class, 'requirement_id');
    }

    public function classType()
    {
        return $this->belongsTo(k12ConnectionCategorizedData::class, 'class_type');
    }

    public function userPreferences()
    {
        return $this->hasMany(UserOpportunityPreference::class, 'opportunity_id');
    }

    public function currentUserPreference()
    {
        return $this->hasOne(UserOpportunityPreference::class, 'opportunity_id')
                    ->where('instructor_id', Auth::guard('instructor')->id())
                    ->select('opportunity_id', 'status');
    }

    public function contracts()
    {
        return $this->hasMany(\App\SchoolRequirementContract::class, 'requirement_id');
    }

    public function timezone()
    {
        return $this->hasOne(k12ConnectionCategorizedData::class, 'id', 'time_zone');
    }

    public function savedByInstructors()
    {
        return $this->userPreferences()->where('status', 'saved');
    }

    public function archivedByInstructors()
    {
        return $this->userPreferences()->where('status', 'archived');
    }


    public function getDetailedScheduleAttribute()
    {
        $merged = [];

        foreach (['regular_days', 'schedule_1_days', 'schedule_2_days'] as $field) {
            $slots = $this->$field ?? [];

            if (is_array($slots)) {
                foreach ($slots as $slot) {
                    if (!isset($slot['day'], $slot['start_time'], $slot['end_time'])) {
                        continue;
                    }

                    $day = $slot['day'];

                    if (!isset($merged[$day])) {
                        $merged[$day] = [];
                    }

                    $merged[$day][] = [
                        'start_time' => $slot['start_time'],
                        'end_time'   => $slot['end_time'],
                    ];
                }
            }
        }

        return $merged;
    }

    public function calculateWorkingLoad()
    {
        $start  = Carbon::parse($this->start_date);
        $end    = Carbon::parse($this->end_date);

        // Parse no_class_dates into Carbon objects
        $skipDates = collect(json_decode($this->no_class_dates))
            ->filter()
            ->map(fn($d) => Carbon::parse(trim($d))->toDateString())
            ->toArray();

        $schedule = $this->detailed_schedule; // already merged accessor

        $totalDays = 0;
        $totalHours = 0;

        foreach (CarbonPeriod::create($start, $end) as $date) {
            $dayName = strtolower($date->format('l')); // monday, tuesday...

            // Skip if in no_class_dates
            if (in_array($date->toDateString(), $skipDates)) {
                continue;
            }

            // Check if schedule exists for this weekday
            if (!isset($schedule[$dayName])) {
                continue;
            }

            $totalDays++;

            foreach ($schedule[$dayName] as $slot) {
                $startTime = Carbon::parse($slot['start_time']);
                $endTime   = Carbon::parse($slot['end_time']);
                $hours     = $endTime->diffInMinutes($startTime) / 60;

                $totalHours += $hours;
            }
        }

        return [
            'working_days'  => $totalDays,
            'working_hours' => $totalHours,
        ];
    }

    /**
     * Get formatted schedule display for contract
     * Format: MON, TUE, WED, THU, FRI, SAT, SUN, 12:00 - 01:00
     */
    public function getScheduleDisplay()
    {
        // Get schedule data from regular_days, schedule_1_days, or schedule_2_days
        $scheduleData = [];

        // Check regular_days first
        if (!empty($this->regular_days)) {
            $scheduleData = is_string($this->regular_days) ? json_decode($this->regular_days, true) : $this->regular_days;
        }
        // If no regular_days, check schedule_1_days
        elseif (!empty($this->schedule_1_days)) {
            $scheduleData = is_string($this->schedule_1_days) ? json_decode($this->schedule_1_days, true) : $this->schedule_1_days;
        }
        // If no schedule_1_days, check schedule_2_days
        elseif (!empty($this->schedule_2_days)) {
            $scheduleData = is_string($this->schedule_2_days) ? json_decode($this->schedule_2_days, true) : $this->schedule_2_days;
        }

        // Ensure we have a valid array
        if (empty($scheduleData) || !is_array($scheduleData)) {
            return 'N/A';
        }

        $dayAbbreviations = [
            'Monday' => 'MON',
            'Tuesday' => 'TUE',
            'Wednesday' => 'WED',
            'Thursday' => 'THU',
            'Friday' => 'FRI',
            'Saturday' => 'SAT',
            'Sunday' => 'SUN'
        ];

        // Define the order of days for proper sorting
        $dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        $scheduledDays = [];
        $timeRange = '';

        // Sort schedule data by day order - only if it's a valid array
        if (is_array($scheduleData)) {
            usort($scheduleData, function($a, $b) use ($dayOrder) {
                if (!is_array($a) || !is_array($b) || !isset($a['day']) || !isset($b['day'])) {
                    return 0;
                }
                $aIndex = array_search($a['day'], $dayOrder);
                $bIndex = array_search($b['day'], $dayOrder);
                $aIndex = $aIndex !== false ? $aIndex : 999;
                $bIndex = $bIndex !== false ? $bIndex : 999;
                return $aIndex <=> $bIndex;
            });
        }

        foreach ($scheduleData as $slot) {
            // Skip if slot is not an array or missing required fields
            if (!is_array($slot) || !isset($slot['day'], $slot['start_time'], $slot['end_time'])) {
                continue;
            }

            $dayName = $slot['day'];

            // Add day abbreviation if it exists and not already added
            if (isset($dayAbbreviations[$dayName]) && !in_array($dayAbbreviations[$dayName], $scheduledDays)) {
                $scheduledDays[] = $dayAbbreviations[$dayName];
            }

            // Get time range from first valid slot
            if (empty($timeRange) && !empty($slot['start_time']) && !empty($slot['end_time'])) {
                try {
                    // Handle different time formats
                    $startTime = $slot['start_time'];
                    $endTime = $slot['end_time'];

                    // If time is already in H:i format, use it directly
                    if (preg_match('/^\d{2}:\d{2}$/', $startTime)) {
                        $timeRange = $startTime . ' - ' . $endTime;
                    } else {
                        // Try to parse and format the time
                        $startTime = Carbon::parse($startTime)->format('H:i');
                        $endTime = Carbon::parse($endTime)->format('H:i');
                        $timeRange = $startTime . ' - ' . $endTime;
                    }
                } catch (\Exception) {
                    // If parsing fails, use the original format
                    $timeRange = $slot['start_time'] . ' - ' . $slot['end_time'];
                }
            }
        }

        if (empty($scheduledDays)) {
            return 'N/A';
        }

        $daysString = implode(', ', $scheduledDays);
        return $timeRange ? $daysString . ', ' . $timeRange : $daysString;
    }

    /**
     * Simple alternative method to get schedule display
     * Fallback method that's more robust
     */
    public function getSimpleScheduleDisplay()
    {
        try {
            // Try to get any schedule data
            $allScheduleFields = ['regular_days', 'schedule_1_days', 'schedule_2_days'];
            $scheduleData = null;

            foreach ($allScheduleFields as $field) {
                $data = $this->$field;
                if (!empty($data)) {
                    $scheduleData = is_string($data) ? json_decode($data, true) : $data;
                    if (is_array($scheduleData) && !empty($scheduleData)) {
                        break;
                    }
                }
            }

            if (empty($scheduleData) || !is_array($scheduleData)) {
                return 'N/A';
            }

            $dayAbbreviations = [
                'Monday' => 'MON', 'Tuesday' => 'TUE', 'Wednesday' => 'WED',
                'Thursday' => 'THU', 'Friday' => 'FRI', 'Saturday' => 'SAT', 'Sunday' => 'SUN'
            ];

            $days = [];
            $time = '';

            foreach ($scheduleData as $slot) {
                if (is_array($slot) && isset($slot['day'])) {
                    $dayName = $slot['day'];
                    if (isset($dayAbbreviations[$dayName])) {
                        $days[] = $dayAbbreviations[$dayName];
                    }

                    // Get time from first slot
                    if (empty($time) && isset($slot['start_time'], $slot['end_time'])) {
                        $time = $slot['start_time'] . ' - ' . $slot['end_time'];
                    }
                }
            }

            if (empty($days)) {
                return 'N/A';
            }

            // Remove duplicates and sort manually
            $days = array_unique($days);
            $orderedDays = [];
            $dayOrder = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

            foreach ($dayOrder as $day) {
                if (in_array($day, $days)) {
                    $orderedDays[] = $day;
                }
            }

            $result = implode(', ', $orderedDays);
            return $time ? $result . ', ' . $time : $result;

        } catch (\Exception) {
            return 'N/A';
        }
    }
}