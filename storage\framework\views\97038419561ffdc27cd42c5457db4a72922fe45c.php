
<!-- ==================== Inline Configuration ==================== -->
<script>
    var token = <?php echo json_encode(session('chatToken') ?? '', 15, 512) ?>;
    window.NODE_URL = "<?php echo e(config('services.node.url')); ?>";
</script>
<script>
    const host = <?php echo json_encode(url('/')); ?>

    var APP_URL = host
</script>
<script src="<?php echo e(asset('js/app.js')); ?>"></script>
<script src="<?php echo e(asset('js/main.js')); ?>"></script>
<script src="<?php echo e(asset('js/image-gallery-magnific-popup.min.js')); ?>"></script>
<script src="//www.gstatic.com/firebasejs/6.2.3/firebase.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-auth.js"></script>
<script src="https://www.gstatic.com/firebasejs/6.2.3/firebase-storage.js"></script>
<script src="//www.gstatic.com/firebasejs/6.2.3/firebase-database.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo e(asset('globals/bootstrap-datepicker.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/custom.js')); ?>?<?php echo 'v=' . rand(); ?>"></script>
<script src="<?php echo e(asset('js/bulkchat.js')); ?>?<?php echo 'v=' . rand(); ?>"></script>
<script src="<?php echo e(asset('js/config.js')); ?>?<?php echo 'v=' . rand(); ?>"></script>
<script src="<?php echo e(asset('js/program.js')); ?>?<?php echo 'v=' . rand(); ?>"></script>
<script src="//cdn.jsdelivr.net/npm/alertifyjs@1.13.1/build/alertify.min.js"></script>
<script src="https://use.fontawesome.com/696d3f7659.js"></script>
<script src="<?php echo e(asset('js/sweetalert2.all.min.js')); ?>"></script>
<script src="<?php echo e(asset('js/jquery.timepicker.min.js')); ?>"></script>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/additional-methods.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>
<script src="https://cdn.ckeditor.com/4.16.2/standard/ckeditor.js"></script>
<script src="<?php echo e(asset('js/admin-custom.js')); ?>"></script>
<script>
    $(document).ready(function() {
        $('.registration-form fieldset:first-child').fadeIn('slow');
        $('.registration-form input[type="text"], .registration-form input[type="password"], .registration-form textarea').on('focus', function() {
            $(this).removeClass('input-error');
        });

        // NEXT STEP
        $('.registration-form .btn-next').on('click', function() {
            var parent_fieldset = $(this).parents('fieldset');
            var next_step = true;

            parent_fieldset.find('input[type="text"], input[type="password"], textarea').each(function() {
                if ($(this).val() == "") {
                    $(this).addClass('input-error');
                    next_step = false;
                } else {
                    $(this).removeClass('input-error');
                }
            });

            if (next_step) {
                parent_fieldset.fadeOut(400, function() {
                    $(this).next().fadeIn();
                });
            }

        });

        // PREVIOUS STEP
        $('.registration-form .btn-previous').on('click', function() {
            $(this).parents('fieldset').fadeOut(400, function() {
                $(this).prev().fadeIn();
            });
        });

        // SUBMIT
        $('.registration-form').on('submit', function(e) {

            $(this).find('input[type="text"], input[type="password"], textarea').each(function() {
                if ($(this).val() == "") {
                    e.preventDefault();
                    $(this).addClass('input-error');
                } else {
                    $(this).removeClass('input-error');
                }
            });
        });
    });


    $(".otpFocus").keyup(function() {
        if (this.value.length == this.maxLength) {
            $(this).next('.otpFocus').focus();
        }
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Select2
        $(".select2").each(function() {
            $(this)
                .wrap("<div class=\"position-relative\"></div>")
                .select2({
                    placeholder: "Select value",
                    dropdownParent: $(this).parent()
                });
        })
        // Daterangepicker
        $("input[name=\"daterange\"]").daterangepicker({
            opens: "left"
        });
        $("input[name=\"datetimes\"]").daterangepicker({
            timePicker: true,
            opens: "left",
            startDate: moment().startOf("hour"),
            endDate: moment().startOf("hour").add(32, "hour"),
            locale: {
                format: "M/DD hh:mm A"
            }
        });

        // Datetimepicker
        $('#datetimepicker-minimum').datetimepicker();
        $('#datetimepicker-view-mode').datetimepicker({
            viewMode: 'years'
        });
        $('#datetimepicker-time').datetimepicker({
            format: 'LT'
        });
        $('#datetimepicker-date').datetimepicker({
            format: 'L'
        });
        var start = moment().subtract(29, "days");
        var end = moment();

        function cb(start, end) {
            $("#reportrange span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"));
        }
        $("#reportrange").daterangepicker({
            startDate: start,
            endDate: end,
            ranges: {
                "Today": [moment(), moment()],
                "Yesterday": [moment().subtract(1, "days"), moment().subtract(1, "days")],
                "Last 7 Days": [moment().subtract(6, "days"), moment()],
                "Last 30 Days": [moment().subtract(29, "days"), moment()],
                "This Month": [moment().startOf("month"), moment().endOf("month")],
                "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
            }
        }, cb);
        cb(start, end);
    });

    $('#datatables-column-search-text-inputs').DataTable({
        "ordering": true,
        "order": [
            [1, 'desc']
        ]
    });
</script>

<!-- Summernote UI -->
<script>
    $('.summernote').each(function() {
        $(this).summernote({
            placeholder: 'Enter description here...',
            tabsize: 2,
            height: 400,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ]
        });
    });
</script>
<!-- Summernote UI End -->

<!-- CKEDITOR UI -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.34/moment-timezone-with-data.min.js"></script>
<script src="<?php echo e(asset('js/timezone.js')); ?>"></script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Apply the ckeditor for all class with name "editor1".
        const loadEditor = () => document.querySelectorAll('.editor1').forEach(function(editor) {
            // Apply the ckeditor to the textarea.
            if(!editor.classList.contains('ckEditorLoaded'))
            CKEDITOR.replace(editor, {
                // provides a image upload option on ckeditor
                filebrowserUploadUrl: "<?php echo e(route('upload.image', ['_token' => csrf_token() ])); ?>",
                on: {
                    instanceReady: function(evt) {
                        editor.classList.add('ckEditorLoaded')
                        evt.editor.on('fileUploadResponse', function(evt) {
                            var xhr = evt.data.fileLoader.xhr,
                            response = JSON.parse(xhr.responseText);
                            if (response.uploaded && response.url) {
                                // setting a initial static style for image.
                                var imgHtml = '<img src="' + response.url + '" style="width:100%;" />';
                                evt.editor.insertHtml(imgHtml);
                                // hides the dialog box after uploading the image.
                                var dialog = CKEDITOR.dialog.getCurrent();
                                if (dialog) {
                                    dialog.hide();
                                }
                                // stop the function.
                                evt.stop();
                            } else {
                                alert('Image upload failed.');
                            }
                        });
                    }
                }
            });
        });

        const timeElements = document.querySelectorAll('.local-time');
        const dateElements = document.querySelectorAll('.local-date');

        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const serverTimezone = <?php echo json_encode(config('app.servertimezone'), 15, 512) ?>;
        timeElements.forEach(function(element) {
            const serverTimeIST = element.dataset.date;

            const serverTimeMoment = moment.tz(serverTimeIST, serverTimezone);
            const localTimeMoment = serverTimeMoment.clone().tz(userTimezone);

            const localTimeFormatted = localTimeMoment.format('MM-DD-YYYY hh:mm A');
            console.log('Local Time:', localTimeFormatted);

            element.textContent = localTimeFormatted;
        });
        dateElements.forEach(function(element) {

            const serverTimeIST = element.dataset.date;

            const serverTimeMoment = moment.tz(serverTimeIST, serverTimezone);
            const localTimeMoment = serverTimeMoment.clone().tz(userTimezone);

            const localTimeFormatted = localTimeMoment.format('MM-DD-YYYY');
            console.log('Local Time:', localTimeFormatted);

            element.textContent = localTimeFormatted;
        });

        loadEditor()
        document.addEventListener('RefreshCKeditor', function(event) {
            loadEditor()
        });
    });
</script>
<!-- CKEDITOR UI End -->
<?php /**PATH D:\whizara\whizara\resources\views/admin/layouts/footerscript.blade.php ENDPATH**/ ?>