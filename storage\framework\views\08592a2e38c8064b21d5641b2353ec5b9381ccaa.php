

<?php $__env->startSection('title'); ?>
Manage Educator | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<main class="content">
    <div class="container-fluid p-0">
        
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-subject-approval/All')); ?>" class="<?php echo e(request()->segment(4) == 'All' ? 'text-primary' : ''); ?>">All</a></li>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-subject-approval/Pending')); ?>" class="<?php echo e((request()->segment(4) == 'Pending') ?'text-primary':''); ?>">Pending</a></li>
                <li class="breadcrumb-item " aria-current="page"><a href="<?php echo e(url('admin/k12connections/manage-subject-approval/Approved')); ?>" class="<?php if (request()->segment(4) == 'Approved') {echo 'text-primary';} ?>">Approved</a></li>
            </ol>
        </nav>
        
        
        <div class="table-responsive">
            <table class="table table-striped" style="width:100%" id="dataTable">
                <thead class="thead-dark">
                    <tr>
                        <th>Instructor ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Subjects</th>
                        <th>Proficiency</th>
                        <th>Lesson Planning</th>
                        <th>Notes</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        
    </div>

    
    <div class="modal fade" id="common-admin-subject-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document" style="max-width: 1020px !important;">
            
        </div>
    </div>
</main>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/datatables.min.css')); ?>">
<script src="<?php echo e(asset('js/datatables.min.js')); ?>"></script>
<script>
    $(function() {
        if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
            dataTable.destroy();
        }
        window.dataTable = initializeAdminDataTable("#dataTable", "<?php echo e(route('admin.manage-subject-approval', request()->segment(4))); ?>",
            [
                {data: 'id', searchable: false, visible: false},
                {data: 'full_name', name: 'full_name'},
                {data: 'email', name: 'email'},
                {data: 'subject_name', name: 'subject_name'},
                {data: 'proficiency', name: 'proficiency'},
                {data: 'lesson_planning', name: 'lesson_planning'},
                {data: 'notes', name: 'notes'},
                {data: 'status', name: 'status'},
                {data: 'action', searchable: false, orderable: false},
            ]
        );

        // *********Approve-Subject*********
        $('#dataTable').on('click', '.btn-approve', function () {
            let url = $(this).data('url');
            let userid = $(this).data('userid');
            let insId = $(this).data('insid');
            let selectedItem = $(this).data('selected-item');

            $.ajax({
                type: "GET",
                url: url,
                dataType: "json",
                success: function (res) {
                    $('#common-admin-subject-modal .modal-dialog').html(res.view);
                    $('#common-admin-subject-modal').attr('data-user-id', userid).attr('data-insid', insId).attr('data-selected-item', selectedItem).modal('show');
                    initializeSelect2();
                },
                error: function (xhr) {
                    alertify.error(xhr.responseJSON?.message || 'An error occurred');
                }
            });
        });

        // *********Reject-Subject*********
        $('#dataTable').on('click', '.btn-reject', function () {
            let url = $(this).data('url');
            let userid = $(this).data('userid');
            let insId = $(this).data('insid');
            let selectedItem = $(this).data('selected-item');

            $.ajax({
                type: "GET",
                url: url,
                dataType: "json",
                success: function (res) {
                    $('#common-admin-subject-modal .modal-dialog').html(res.view);
                    $('#common-admin-subject-modal').attr('data-user-id', userid).attr('data-insid', insId).attr('data-selected-item', selectedItem).modal('show');
                    initializeSelect2();
                },
                error: function (xhr) {
                    alertify.error(xhr.responseJSON?.message || 'An error occurred');
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/subject-approval/index.blade.php ENDPATH**/ ?>