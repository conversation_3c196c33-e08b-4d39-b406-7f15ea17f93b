 <?php echo $__env->make('admin.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 <?php $__env->startSection('body'); ?>
 <?php echo $__env->yieldSection(); ?>
<body data-theme="default" data-layout="fluid" data-sidebar-position="left" data-sidebar-behavior="sticky">
    <!-- LOADER START -->
     <div class="loader d-none" >
        <div class="boxLoading">
        </div>
    </div> 
    <!-- LOADER END -->



    <div class="wrapper">
        <!-- SIDE NAVIGATION START -->
        <?php echo $__env->make('admin.layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- SIDE NAVIGATION END -->

        <div class="main">
            <!-- TOP NAVIGATION START -->
             <?php echo $__env->make('admin.layouts.top', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- TOP NAVIGATION END -->


            <!-- MAIN SECTION START -->
            <main class="content">
               <?php echo $__env->yieldContent('content'); ?>
            </main>
            <!-- MAIN SECTION END -->

            <!-- FOOTER SECTION START -->



              <?php echo $__env->make('admin.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- FOOTER SECTION END -->
        </div>
    </div>
<script>
var base_url = "<?php echo e(url('/')); ?>/";
var _token = "<?php echo e(csrf_token()); ?>";
</script>
 <?php echo $__env->make('admin.layouts.footerscript', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

 <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\whizara\whizara\resources\views/admin/layouts/master.blade.php ENDPATH**/ ?>