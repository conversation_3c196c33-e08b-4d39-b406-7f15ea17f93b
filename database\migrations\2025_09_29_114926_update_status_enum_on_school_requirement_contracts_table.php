<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateStatusEnumOnSchoolRequirementContractsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("
            ALTER TABLE `school_requirement_contracts` 
            MODIFY COLUMN `status` ENUM(
                'draft',
                'pending_signature',
                'pending_approval',
                'in_review',
                'approved',
                'on_hold',
                'cancelled',
                'completed'
            ) NOT NULL DEFAULT 'draft'
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("
            ALTER TABLE `school_requirement_contracts` 
            MODIFY COLUMN `status` ENUM(
                'draft',
                'pending_approval',
                'in_review',
                'approved',
                'on_hold',
                'cancelled',
                'completed'
            ) NOT NULL DEFAULT 'draft'
        ");
    }
}
