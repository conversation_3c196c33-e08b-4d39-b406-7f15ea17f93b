<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SchoolContractContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if School Contract content already exists
        $existingContent = DB::table('tbl_content_settings')
            ->where('type', 'School Contract')
            ->where('locale', 'en')
            ->first();

        if (!$existingContent) {
            DB::table('tbl_content_settings')->insert([
                'type' => 'School Contract',
                'description' => '
                <p>Agreement (&ldquo;Agreement&rdquo;) is made and entered into as of {{DATE}} (the &ldquo;Effective Date&rdquo;) by and between Learn2Code.Live LLC&nbsp; (DBA: Whizara) (the &ldquo;Company&rdquo;), a Texas corporation, and {{ORGANIZATION_NAME}} (&ldquo;Client&rdquo;). (each herein referred to individually as a &ldquo;Party,&rdquo; or collectively as the &ldquo;Parties&rdquo;)</p>

                <p>In consideration of the mutual promises contained herein, the Parties agree as follows:&nbsp;</p>

                <h3>1. TEACHER OF RECORD</h3>

                <p>The Client agrees to directly collaborate with the selected educator as the teacher of record on curriculum needs, alignment, pacing, grading and non-instructional hours per section 2 (Class Details).</p>

                <h3>2. CLASS DETAILS</h3>

                <p> Overview</p>

                <ul>
                    <li>District name: {{DISTRICT_NAME}}</li>
                    <li>School name: {{SCHOOL_NAME}}</li>
                    <li>Delivery mode: {{DELIVERY_MODE}}</li>
                    <li>Class type: {{CLASS_TYPE}}</li>
                    <li>NCES course: {{COURSE_CODE}}</li>
                    <li>Grade level(s): {{GRADE_LEVELS}}</li>
                    <li>Expected class size: {{CLASS_SIZE}}</li>
                </ul>

                <p> Schedule</p>

                <ul>
                    <li>Number of instructional days: {{INSTRUCTIONAL_DAYS}}</li>
                    <li>Class duration: {{CLASS_DURATION}}</li>
                    <li>Number of Non-instructional days: {{NON_INSTRUCTIONAL_DAYS}}</li>
                    <li>Class start date: {{START_DATE}}</li>
                    <li>Class end date: {{END_DATE}}</li>
                    <li>Schedule type: {{SCHEDULE_TYPE}}</li>
                    <li>Class days &amp; times: {{SCHEDULES}}</li>
                </ul>

                <p> Educator Requirements</p>

                <ul>
                    <li>Sp. Ed. certification required: {{SPECIAL_ED_CERT_REQUIRED}}</li>
                    <li>ESOL required: {{ESOL_REQUIRED}}</li>
                    <li>Language of instruction: {{TEACHING_LANGUAGES}}</li>
                    <li>Other Requirements: {{OTHER_REQUIREMENTS}}</li>
                    <li>Educator will use my school-provided curriculum and teaching materials on the school&rsquo;s LMS: {{USE_SCHOOL_LMS}}</li>
                    <li>Educator will have access to class schedule and will input grades in my school&rsquo;s SIS system: {{USE_SCHOOL_SIS}}</li>
                </ul>

                <h3>3. SERVICE DETAILS</h3>

                <p>Whizara provides:</p>

                <ul>
                    <li>High-quality US certified Teacher of Record (Checkr background checked and Whizara Remote classroom management certified)</li>
                    <li>Substitutes for the Teacher of Record as needed if requested by Educator or the school staff</li>
                    <li>Replacement for the Teacher of Record if needed (Client selected or Whizara assigned per Client&rsquo;s preference)</li>
                    <li>Quality control of all live classes through built-in feedback mechanism</li>
                    <li>Recommendations for teacher replacement&nbsp;</li>
                    <li>School-provided proctor training for efficient collaboration with Educator</li>
                    <li>Videoconferencing software</li>
                    <li>A dedicated Operations Manager for implementation and support</li>
                    <li>Weekly and monthly reports on instructional days and non-instructional hours completed</li>
                </ul>

                <p>District/School provides:</p>

                <ul>
                    <li>1 Proctor (paraprofessional) for each class period (proctor to complete Whizara provided proctor training)</li>
                    <li>1 Point of contact at the school administration level</li>
                    <li>Classroom space, LCD Screen or Projector and Screen, Camera, Microphone, Headphone, Adequate internet access and bandwidth, laptop per student, Class rosters and bell schedule</li>
                    <li>District and school calendars and school closure days as a part of onboarding process</li>
                    <li>1 point of contact in the IT department for any network/login troubleshooting.</li>
                </ul>

                <h3>4. COST AND PAYMENTS</h3>

                <p>TOTAL COST: $ {{TOTAL_COST}}</p>

                <ul>
                    <li>The total cost includes a setup fee: $ {{SETUP_FEE}}</li>
                    <li>The total cost includes a supplies fee: $ {{SUPPLIES_FEE}}</li>
                    <li>Any additional instructional days beyond the agreed upon number of instructional days will be automatically adjusted to the contract at the end of the contract term.</li>
                    <li>Any additional non-instructional hours beyond the agreed upon number of non-instructional hours will be automatically adjusted to the contract at the end of the contract term.</li>
                    <li>Schedule Changes: A {{SCHEDULE_CHANGE_FEE}} fee will apply to any schedule changes made less than 2 weeks before the program start date.</li>
                    <li>Educators are Checkr background checked. Any additional background checks will incur additional fee.</li>
                    <li>For credit card payments: A {{CARD_PROCESSING_FEE}} credit card processing fee is applicable.</li>
                </ul>

                <p>INVOICE SCHEDULE:</p>

                <p>{{INVOICE_SCHEDULE}}</p>

                <p>PAYMENT TERMS</p>

                <ul>
                    <li>Payment due net 30 days</li>
                </ul>

                <p>ACCOUNTING/INVOICE CONTACT</p>

                <ul>
                    <li>The Client shall provide the name, email address, and phone number of the appropriate accounts payable contact for invoicing and payment related questions.</li>
                </ul>

                <p>By continuing, you acknowledge that you understand and agree to the Whizara K12 Connections Terms and Privacy Policy.</p>

                <p>This process is accepted and agreed upon as the date first written above.</p>

                <p><strong>Signature:</strong></p>

                <p>Name: {{LEGEL_USER_NAME}}<br />
                Title: {{LEGEL_USER_TITLE}}<br />
                Organization: {{CLIENT_NAME}}<br />
                Address: {{CLIENT_ADDRESS}}<br />
                Email: {{CONTACT_EMAIL}}<br />
                Phone number: {{CONTACT_NUMBER}}<br />
                <br />
                <br />
                Name: {{SALES_NAME}}<br />
                Title: {{SALES_TITLE}}<br />
                Organization: Learn2Code.Live (DBA: Whizara)<br />
                Email: {{SALES_EMAIL}}<br />
                Phone: {{SALES_CONTACT_NO}}<br />
                Address: {{SALES_ADDRESS}}</p>
                ',
                'document' => null,
                'locale' => 'en',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
